import express, { Request, Response, NextFunction } from "express";
import {
    validateError,
    fileNameTimestamp,
    generateZip,
    generateTimeSeries,
    canAccessVessel,
    groupArtifactsByDuplicateIndex,
    userHasPermissions,
    groupByImage,
} from "../utils/functions";
import db from "../modules/db";
import { validateData } from "../middlewares/validator";
import { body, param, query } from "express-validator";
import limitPromise from "../modules/pLimit";
import mongoose, { isValidObjectId } from "mongoose";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import compression from "compression";
import { getObjectStream, processBatchItem, s3Config } from "../modules/awsS3";
import dayjs from "dayjs";
import { defaultDateTimeFormat } from "../utils/timezonesList";
import vesselService from "../services/Vessel.service";
import artifactFlagService from "../services/ArtifactFlag.service";
import { permissions } from "../utils/permissions";
import hasPermission from "../middlewares/hasPermission";
import ioEmitter from "../modules/ioEmitter";
import Vessel from "../models/Vessel";
import { IArtifact, IArtifactFilters, IGroupedArtifacts } from "../interfaces/Artifact";
import { IQueryFilter } from "src/interfaces/Common";

const router = express.Router();

router.use(compression());

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

const conditionalApiLimiter = (req: Request, res: Response, next: NextFunction) => {
    if (req.path.match(/\/link\//) || req.header("Fetch-From") === "dashboard") {
        // Skip rate limiting for excluded paths
        next();
    } else {
        // Apply rate limiting for other paths
        apiLimiter(req, res, next);
    }
};

const capitalize = (str: string) => str.replace(/\b\w/g, (c) => c.toUpperCase());
const dedupeNormalize = (arr: (string | null)[]) => {
    const seen = new Set();
    return arr
        .filter((item): item is string => {
            if (!item || typeof item !== "string") return false;
            const norm = item.trim().toLowerCase();
            if (seen.has(norm)) return false;
            seen.add(norm);
            return true;
        })
        .map((item) => capitalize(item.trim().toLowerCase()));
};

const defaultProjection = {
    _id: 1,
    unit_id: 1,
    bucket_name: 1,
    image_path: 1,
    video_path: 1,
    location: 1,
    category: 1,
    super_category: 1,
    size: 1,
    color: 1,
    weapons: 1,
    others: 1,
    timestamp: 1,
    onboard_vessel_name: 1,
    onboard_vessel_id: 1,
    portal: 1,
    country_flag: 1,
    aws_region: 1,
    text_extraction: 1,
    imo_number: 1,
    thumbnail_image_path: 1,
    vessel_features: 1,
    home_country: 1,
    vessel_orientation: 1,
    true_bearing: 1,
    det_nbbox: 1,
};

router.use("/", conditionalApiLimiter);

router.get(
    "/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACTS_BULK),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("vesselIds")
                    .isString()
                    .withMessage(`vesselIds is a required string`)
                    .notEmpty()
                    .withMessage(`vesselIds must be a comma-separated string`)
                    .if(query("vesselIds").exists())
                    .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim()))
                    .custom((v: string[]) => v.every((id: string) => isValidObjectId(id)))
                    .withMessage(`vesselIds must be valid object IDs`),
                query("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
                query("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselIds, startTimestampISO, endTimestampISO } = req.query as {
                vesselIds: string[];
                startTimestampISO?: string;
                endTimestampISO?: string;
            };
            console.log(`/artifacts/bulk ${vesselIds}`, startTimestampISO, endTimestampISO);

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });

            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            const query: IQueryFilter = { "portal.is_archived": { $ne: true } };
            query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };

            if (startTimestampISO) {
                const endTime = endTimestampISO || Date.now();
                query.timestamp = { $gt: new Date(startTimestampISO), $lt: new Date(endTime) };
            }
            query.location = { $ne: null };
            // query.host_vessel = { $ne: true };
            query.vessel_presence = true;
            query.super_category = { $ne: null };

            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/artifacts/bulk querying DB query: ${JSON.stringify(query)}`);

                const ts = new Date().getTime();
                const cursor = db.qmai.collection("analysis_results").find(query, {
                    projection: {
                        _id: 1,
                        timestamp: 1,
                        location: 1,
                        onboard_vessel_id: 1,
                        video_path: 1,
                        super_category: 1,
                        image_path: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                const result = (await cursor.toArray()).map(({ video_path, ...artifact }) => {
                    return {
                        ...artifact,
                        video_exists: video_path ? true : false,
                    };
                });

                console.log(`/artifacts/bulk time taken to query artifacts ${new Date().getTime() - ts}`);

                // Apply unified logic: group by image_path and create duplications
                const unifyingArtifacts = groupByImage(result as IArtifact[]);
                console.log("/artifacts/bulk after groupByImage", unifyingArtifacts.length);
                const groupedArtifacts: IGroupedArtifacts = (unifyingArtifacts as IArtifact[]).reduce((acc, artifact) => {
                    const vesselId = artifact.onboard_vessel_id.toString();
                    if (!acc[vesselId]) {
                        acc[vesselId] = [];
                    }
                    acc[vesselId].push(artifact);
                    return acc;
                }, {} as IGroupedArtifacts);

                vesselIds.forEach((vesselId: string) => {
                    if (!groupedArtifacts[vesselId]) {
                        groupedArtifacts[vesselId] = [];
                    }
                });

                return groupedArtifacts;
            });
            // console.log(`/artifacts/bulk received ${artifacts.length} artifacts`);

            if (isClosed) return res.end();
            res.json({
                artifacts,
            });
            console.log(`/artifacts/bulk time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get("/filters", assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACT_FILTERS), isAuthenticated, async (req, res) => {
    try {
        const countryFlags = await db.qm.collection("notification_flags").find().toArray();
        const superCategories = await db.qm.collection("notification_categories").find().project({ name: 1, code: 1 }).toArray();
        const sizesRaw = await db.qmai.collection("analysis_results").distinct("size", { size: { $ne: null } });
        const weaponsAgg = await db.qmai
            .collection("analysis_results")
            .aggregate([
                { $match: { weapons: { $ne: null } } },
                { $group: { _id: "$weapons", count: { $sum: 1 } } },
                { $match: { count: { $gt: 10 } } },
                { $sort: { count: -1 } },
                { $project: { _id: 0, name: "$_id", count: 1 } },
            ])
            .toArray();
        const weapons = weaponsAgg.map((w) => w.name);
        const sizes = dedupeNormalize(sizesRaw);

        const filterItems = {
            superCategories: superCategories.map((itm) => itm.name),
            countryFlags: countryFlags.map((itm) => {
                return { name: itm.name, code: itm.code };
            }),
            sizes,
            weapons,
        };

        res.status(200).json(filterItems);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/:vesselName",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACTS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v: string[]) => v.map((id: string) => new mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselName } = req.params;
            const { startTimestamp, endTimestamp, excludeIds } = req.body;
            console.log(`/artifacts ${vesselName}`, startTimestamp, endTimestamp);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findByAssignedUnitId({ unitId: vesselName });
            if (!vessel) return res.status(404).json({ message: "Unit does not exist or is no longer active" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access artifacts for '${vesselName}'` });
            }

            const query: IQueryFilter = { "portal.is_archived": { $ne: true } };
            query.onboard_vessel_id = vessel._id;
            // query.host_vessel = { $ne: true };
            query.location = { $ne: null };
            query.vessel_presence = true;
            query.super_category = { $ne: null };
            if (startTimestamp) {
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTimestamp || Date.now()) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/artifacts ${vesselName} querying DB`);
                const cursor = db.qmai.collection("analysis_results").find(query, {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        onboard_vessel_id: 1,
                        portal: 1,
                        bucket_name: 1,
                        aws_region: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        others: 1,
                        timestamp: 1,
                        text_extraction: 1,
                        weapons: 1,
                        imo_number: 1,
                        thumbnail_image_path: 1,
                        true_bearing: 1,
                        det_nbbox: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                return await cursor.toArray();
            });
            console.log(`/artifacts ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/artifacts ${vesselName} received ${Array.isArray(artifacts) && artifacts.length} artifacts`);
            if (isClosed) return res.end();
            res.json(artifacts);
            console.log(`/artifacts ${vesselName} time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_PAGINATED_ARTIFACTS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v: string[]) => v.map((id: string) => new mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("page").isInt({ min: 1 }).default(1).withMessage("Page must be a positive integer"),
                body("pageSize").isInt({ min: 1 }).default(20).withMessage("Page size must be a positive integer"),
                body("filters").isObject().withMessage("Filters must be a valid JSON object").optional(),
                body("group")
                    .isInt({ min: 0, max: 1 })
                    .optional()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("projection")
                    .isObject()
                    .optional()
                    .withMessage("Projection must be a valid JSON object")
                    .custom((projection) => {
                        if (!projection) return true; // Allow empty projection

                        const allowedFields = Object.entries(defaultProjection)
                            .map(([key, value]) => (value ? key : null))
                            .filter(Boolean);

                        const projectionKeys = Object.keys(projection);
                        const invalidKeys = projectionKeys.filter((key) => !allowedFields.includes(key));

                        if (invalidKeys.length > 0) {
                            throw new Error(`Invalid projection fields: ${invalidKeys.join(", ")}. Allowed fields: ${allowedFields.join(", ")}`);
                        }

                        return true;
                    }),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { excludeIds, page, pageSize, filters, group, projection } = req.body as {
                excludeIds?: string[];
                page: number;
                pageSize: number;
                filters?: IArtifactFilters;
                group?: number;
                projection?: IQueryFilter;
            };
            console.log(`/artifacts`);

            const query: IQueryFilter = { "portal.is_archived": { $ne: true } };
            if (excludeIds) query._id = { $nin: excludeIds };
            query.location = { $ne: null };
            query.vessel_presence = true;
            query.super_category = { $ne: null };

            let allowedVessels = [];
            const allVessels = await vesselService.find({}, { _id: 1, is_active: 1, region_group_id: 1 });
            allowedVessels = allVessels.filter((v) => canAccessVessel(req, v)).map((v) => v._id.toString());
            query.onboard_vessel_id = { $in: allowedVessels.map((v: string) => new mongoose.Types.ObjectId(v)) };
            // console.log("allowedVessels", allowedVessels);

            if (filters) {
                const { start_time, end_time, categories, id, colors, sizes, type, vessel_ids, country_flags, weapons, host_vessel } = filters;
                if (id) {
                    query._id = new mongoose.Types.ObjectId(id);
                } else {
                    if (start_time && end_time) query.timestamp = { $gt: new Date(start_time), $lt: new Date(end_time) };
                    if (categories) query.super_category = { $in: categories };
                    if (colors && Array.isArray(colors) && colors.length > 0) {
                        query.$and = colors.map((color) => ({
                            color: { $regex: new RegExp(color, "i") },
                        }));
                    }
                    if (sizes && Array.isArray(sizes) && sizes.length > 0) {
                        query.$or = sizes.map((size) => ({
                            size: { $regex: new RegExp(`^${size}$`, "i") },
                        }));
                    }
                    if (vessel_ids) {
                        const filteredVesselIds = vessel_ids.filter((v: string) => allowedVessels.includes(v));
                        if (filteredVesselIds.length === 0) {
                            return res.status(403).json({ message: `Cannot access artifacts for '${vessel_ids}'` });
                        }
                        query.onboard_vessel_id = { $in: filteredVesselIds.map((v: string) => new mongoose.Types.ObjectId(v)) };
                    }
                    if (country_flags) query.country_flag = { $in: country_flags };
                    if (weapons && Array.isArray(weapons) && weapons.length > 0) {
                        query.weapons = { $in: weapons };
                    }
                }
                if (type) {
                    if (type === "video") {
                        query.video_path = { $exists: true, $ne: null };
                    } else if (type === "image") {
                        query.video_path = { $exists: false };
                    }
                }
                if (host_vessel === true) {
                    query.host_vessel = true;
                } else if (host_vessel === false) {
                    query.host_vessel = { $ne: true };
                }
            }

            const skip = (page - 1) * pageSize;
            const limit = pageSize;

            const totalCount = await db.qmai.collection("analysis_results").countDocuments(query);
            const artifacts = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/artifacts querying DB`);
                return await db.qmai
                    .collection("analysis_results")
                    .find(query)
                    .sort({ timestamp: -1 }) // Sort by timestamp in descending order
                    .project(projection || defaultProjection)
                    .skip(skip)
                    .limit(limit)
                    .toArray();
            });
            console.log(`/artifacts time taken to query ${new Date().getTime() - ts}`);
            console.log(`/artifacts received ${Array.isArray(artifacts) && artifacts.length} artifacts`);

            if (isClosed) return res.end();

            // filter the unique artifacts based on image_path
            const unifyingArtifacts = groupByImage(artifacts as IArtifact[]);
            console.log("/artifacts after  groupByImage", unifyingArtifacts.length);
            let groupedArtifacts;
            if (group && Array.isArray(unifyingArtifacts) && unifyingArtifacts.length > 0) {
                groupedArtifacts = groupArtifactsByDuplicateIndex(unifyingArtifacts as IArtifact[], 0.7);
                console.log(
                    `/artifacts grouped ${Array.isArray(groupedArtifacts) && groupedArtifacts.length} groups from ${Array.isArray(artifacts) && artifacts.length} artifacts`,
                );
            }

            const data = {
                artifacts: unifyingArtifacts,
                page,
                pageSize,
                totalCount,
                ...(groupedArtifacts && { groupedArtifacts }),
            };
            res.json(data);
            console.log(`/artifacts time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.get(
    "/detail/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACT_DETAIL),
    isAuthenticated,
    validateData.bind(this, [param("id").isMongoId().withMessage("Invalid artifact ID")]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;

            const artifact = await db.qmai.collection("analysis_results").findOne(
                { _id: new mongoose.Types.ObjectId(id) },
                {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        aws_region: 1,
                        image_path: 1,
                        video_path: 1,
                        thumbnail_image_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        others: 1,
                        timestamp: 1,
                        text_extraction: 1,
                        weapons: 1,
                        imo_number: 1,
                        onboard_vessel_id: 1,
                        onboard_vessel_name: 1,
                        portal: 1,
                        country_flag: 1,
                        vessel_features: 1,
                        home_country: 1,
                        vessel_orientation: 1,
                        true_bearing: 1,
                        det_nbbox: 1,
                    },
                },
            );

            if (!artifact) {
                return res.status(404).json({ message: "Artifact not found" });
            }

            if (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels])) {
                if (!artifact.onboard_vessel_id) {
                    return res.status(403).json({ message: "Cannot access this artifact" });
                }

                const vessel = await Vessel.findById(artifact.onboard_vessel_id);

                if (!vessel || !canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: "Cannot access this vessel artifact" });
                }
            }

            if (artifact.image_path) {
                artifact.image_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }

            if (artifact.video_path) {
                artifact.video_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.video_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }

            if (artifact.thumbnail_image_path) {
                artifact.thumbnail_url = processBatchItem({
                    bucketName: s3Config.buckets.compressedItems.name as string,
                    key: artifact.thumbnail_image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }

            res.json(artifact);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post("/:id/download", assignEndpointId.bind(this, endpointIds.DOWNLOAD_ARTIFACT), isAuthenticated, async (req, res) => {
    try {
        const { id } = req.params;

        const artifact = await db.qmai.collection("analysis_results").findOne({ _id: new mongoose.Types.ObjectId(id) });

        if (!artifact) {
            return res.status(404).json({ message: "Artifact not found" });
        }

        const getMetadata = () => {
            return `
                Timestamp: ${dayjs(artifact.timestamp).format(req.user.date_time_format || defaultDateTimeFormat)} 
                Category: ${artifact.super_category}
                Sub Category: ${artifact.category}
                Color: ${artifact.color}
                Size: ${artifact.size}
                Location: ${(artifact.location?.coordinates || [])[0]}, ${(artifact.location?.coordinates || [])[1]}
                Others: ${artifact.others}
            `;
        };

        const filenameMask = `${fileNameTimestamp()} - ${(artifact.onboard_vessel_name || artifact.unit_id).replace(/ /g, "_")}`;

        const mediaKey = artifact.video_path || artifact.image_path;
        const mediaPathParts = mediaKey.split(".");
        const extension = mediaPathParts[mediaPathParts.length - 1];

        const mediaData = await getObjectStream(artifact.bucket_name, artifact.aws_region, mediaKey);

        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: mediaData,
            },
            {
                name: `${filenameMask}.txt`,
                content: getMetadata(),
            },
        ];

        const zip = generateZip(filesData as { name: string; content: string | Buffer<ArrayBufferLike> }[]);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });

        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);

        await new Promise((resolve, reject) => {
            mediaData.on("error", (err) => {
                console.error("S3 stream error:", err);
                reject(err);
            });

            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                    reject(e);
                });
        });
    } catch (err) {
        validateError(err, res);
    }
});

router.get(
    "/hoursAggregatedCount",
    assignEndpointId.bind(this, endpointIds.FETCH_HOURS_AGGREGATED_COUNT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("startTimestamp").isInt().toInt().withMessage("Invalid startTimestamp"),
                query("endTimestamp").isInt().toInt().withMessage("Invalid endTimestamp"),
                query("interval").isInt({ min: 1 }).toInt().withMessage("Interval must be a positive integer"),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        try {
            const { startTimestamp, endTimestamp, interval } = req.query as { startTimestamp: string; endTimestamp: string; interval: string };

            const startDate = new Date(Number(startTimestamp));
            const endDate = new Date(Number(endTimestamp));

            const aggregatedData = await db.qmai
                .collection("analysis_results")
                .aggregate([
                    { $match: { timestamp: { $gte: startDate, $lt: endDate }, vessel_presence: true, super_category: { $ne: null } } },
                    {
                        $group: {
                            _id: {
                                $dateTrunc: {
                                    date: "$timestamp",
                                    unit: "minute",
                                    binSize: interval,
                                },
                            },
                            count: { $sum: 1 },
                        },
                    },
                    {
                        $project: {
                            timestamp: "$_id",
                            count: 1,
                            _id: 0,
                        },
                    },
                    { $sort: { timestamp: 1 } },
                ])
                .toArray();

            const statistics = aggregatedData.reduce((acc, entry) => {
                acc[entry.timestamp.toISOString()] = entry.count;
                return acc;
            }, {});

            const concatenatedTimeSeries = {
                ...generateTimeSeries(Number(startTimestamp), Number(endTimestamp), Number(interval)),
                ...statistics,
            };
            const convertedToArray = Object.entries(concatenatedTimeSeries)
                .map(([key, value]) => ({
                    timestamp: key,
                    count: value,
                }))
                .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            res.status(200).json(convertedToArray);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id/archive",
    assignEndpointId.bind(this, endpointIds.ARCHIVE_ARTIFACT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const userId = req.user._id;
            const result = await db.qmai.collection("analysis_results").findOneAndUpdate(
                { _id: new mongoose.Types.ObjectId(id) },
                {
                    $set: {
                        portal: {
                            is_archived: true,
                            archived_by: userId,
                            archived_at: new Date(),
                        },
                    },
                },
                { returnDocument: "after" },
            );
            if (!result.value) return res.status(404).json({ message: "Artifact not found" });

            ioEmitter.emit("notifyAll", {
                name: "artifact/changed",
                data: { artifact: result.value, action: "archived" },
            });

            res.json({ message: "Artifact archived successfully", artifact: result.value });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id/unarchive",
    assignEndpointId.bind(this, endpointIds.UNARCHIVE_ARTIFACT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const result = await db.qmai.collection("analysis_results").findOneAndUpdate(
                { _id: new mongoose.Types.ObjectId(id) },
                {
                    $set: {
                        portal: {
                            is_archived: false,
                            archived_by: null,
                            archived_at: null,
                        },
                    },
                },
                { returnDocument: "after" },
            );
            if (!result.value) return res.status(404).json({ message: "Artifact not found" });

            ioEmitter.emit("notifyAll", {
                name: "artifact/changed",
                data: { artifact: result.value, action: "unarchived" },
            });

            res.json({ message: "Artifact unarchived successfully", artifact: result.value });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/archived",
    assignEndpointId.bind(this, endpointIds.FETCH_ARCHIVED_ARTIFACTS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    (req, res, next) =>
        validateData(
            [
                query("page").isInt({ min: 1 }).toInt().withMessage("Page must be a positive integer"),
                query("pageSize").isInt({ min: 1 }).toInt().withMessage("Page size must be a positive integer"),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        try {
            const { page = 1, pageSize = 100 } = req.query as { page?: string; pageSize?: string };
            console.log(`/artifacts/archived`);

            const query: IQueryFilter = { "portal.is_archived": true, vessel_presence: true, super_category: { $ne: null } };
            const skip = (Number(page) - 1) * Number(pageSize);
            const limit = Number(pageSize);

            const totalCount = await db.qmai.collection("analysis_results").countDocuments(query);
            const artifacts = await db.qmai
                .collection("analysis_results")
                .find(query)
                .sort({ "portal.archived_at": -1 })
                .project({
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    thumbnail_image_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    portal: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                    true_bearing: 1,
                    det_nbbox: 1,
                })
                .skip(skip)
                .limit(limit)
                .toArray();

            console.log(`/artifacts/archived received ${artifacts?.length} artifacts`);

            res.json({
                artifacts,
                page,
                pageSize,
                totalCount,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

//This route is used for fetching the artifact indicator activity
router.post(
    "/activityIndicators/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_ARTIFACT_INDICATORS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("vesselIds")
                    .isArray({ min: 1 })
                    .customSanitizer((v: string[]) => v.map((id: string) => new mongoose.Types.ObjectId(id)))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        try {
            const { vesselIds, startTimestamp, endTimestamp } = req.body;

            if (!endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp & endTimestamp is required" });
            }
            // Only allow vesselIds that user can access
            const allowedVesselIds = [];
            for (const vesselId of vesselIds) {
                // Optionally, you can fetch vessel and check access
                const vessel = await vesselService.findById({ id: vesselId });
                if (vessel && canAccessVessel(req, vessel)) {
                    allowedVesselIds.push(vesselId);
                }
            }
            if (allowedVesselIds.length === 0) {
                return res.status(403).json({ message: "No accessible vessels" });
            }
            // Query all artifacts for allowed vessels in one go
            const aggregationPipeline = [
                {
                    $match: {
                        onboard_vessel_id: { $in: allowedVesselIds },
                        "portal.is_archived": { $ne: true },
                        location: { $ne: null },
                        vessel_presence: true,
                        super_category: { $ne: null },
                        timestamp: {
                            $gt: new Date(startTimestamp),
                            $lt: new Date(endTimestamp),
                        },
                    },
                },
                {
                    $project: {
                        _id: { $toString: "$_id" },
                        onboard_vessel_id: { $toString: "$onboard_vessel_id" },
                    },
                },
                {
                    $group: {
                        _id: "$onboard_vessel_id",
                        artifacts: { $push: { _id: "$_id", onboard_vessel_id: "$onboard_vessel_id" } },
                    },
                },
            ];

            const groupedResults = await db.qmai.collection("analysis_results").aggregate(aggregationPipeline).toArray();

            res.json({
                latestArtifacts: groupedResults,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id/flag",
    assignEndpointId.bind(this, endpointIds.FLAG_ARTIFACT),
    isAuthenticated,
    validateData.bind(this, [param("id").isMongoId().withMessage("Invalid artifact ID")]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const userId = req.user._id;

            const flag = await artifactFlagService.flagArtifact(id, userId as string);
            res.json({ message: "Artifact flagged successfully", flag });
        } catch (err: any) {
            if (err.message === "Artifact not found") {
                return res.status(404).json({ message: err.message });
            }
            if (err.message === "You have already flagged this artifact") {
                return res.status(400).json({ message: err.message });
            }
            validateError(err, res);
        }
    },
);

router.post("/:id/unflag", assignEndpointId.bind(this, endpointIds.UNFLAG_ARTIFACT), isAuthenticated, async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user._id;
        const flag = await artifactFlagService.unflagArtifact(id, userId as string);
        res.json({ message: "Artifact unflagged successfully", flag });
    } catch (err: any) {
        if (err.message === "Flag not found") {
            return res.status(404).json({ message: err.message });
        }
        validateError(err, res);
    }
});

router.get(
    "/flagged",
    assignEndpointId.bind(this, endpointIds.FETCH_FLAGGED_ARTIFACTS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    async (req: Request, res: Response) => {
        try {
            const artifacts = await artifactFlagService.getFlaggedArtifacts();
            res.json(artifacts);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get("/flagged/user", assignEndpointId.bind(this, endpointIds.FETCH_USER_FLAGGED_ARTIFACT_IDS), isAuthenticated, async (req, res) => {
    try {
        const userId = req.user._id;
        const artifactIds = await artifactFlagService.getUserFlaggedArtifactIds(userId as string);
        res.json({ flaggedArtifactIds: artifactIds });
    } catch (err) {
        validateError(err, res);
    }
});

router.delete(
    "/:id/flags",
    assignEndpointId.bind(this, endpointIds.REMOVE_ALL_FLAGS_FROM_ARTIFACT),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    validateData.bind(this, [param("id").isMongoId().withMessage("Invalid artifact ID")]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const result = await artifactFlagService.removeAllFlagsFromArtifact(id);
            res.json({
                message: "All flags removed from artifact successfully",
                deletedCount: result.deletedCount,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * tags:
 *   name: Artifacts
 *   description: Fetch vessel artifacts data
 * components:
 *   schemas:
 *     Artifact:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document id of the artifact.
 *           example: "66ee885da6db303e08c075cb"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the artifact was recorded.
 *           example: "2024-09-21T08:41:43.736Z"
 *         bucket_name:
 *           type: string
 *           description: Name of the S3 bucket where the artifact is stored.
 *           example: "smartmast-prototype-33-ap"
 *         unit_id:
 *           type: string
 *           description: Identifier for the vessel.
 *           example: "prototype-33"
 *         image_path:
 *           type: string
 *           description: Path to the artifact image in the S3 bucket.
 *           example: "artifacts/2024-09-21T07:22:34.941Z/image/prototype-33_cam-1_2024-09-21T08:41:43.736Z.jpg"
 *         location:
 *           type: object
 *           nullable: true
 *           properties:
 *             type:
 *               type: string
 *               description: Type of the location.
 *               example: "Point"
 *             coordinates:
 *               type: array
 *               items:
 *                 type: number
 *               description: Coordinates of the location.
 *               example: [12.5, 117.2075473]
 *         category:
 *           type: string
 *           nullable: true
 *           description: Category of the object in the artifact.
 *           example: "Banca"
 *         super_category:
 *           type: string
 *           nullable: true
 *           description: Broad classification of the object.
 *           example: "Fishing"
 *         color:
 *           type: string
 *           nullable: true
 *           description: Color description of the object in the artifact.
 *           example: "Gray and white"
 *         size:
 *           type: string
 *           nullable: true
 *           description: Size of the object.
 *           example: "Small"
 *         text_extraction:
 *           type: array
 *           description: Text extraction from the artifact.
 *           items:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text extracted from the artifact.
 *                 example: "Traditional outrigger design, likely used for small-scale fishing."
 *               confidence:
 *                 type: number
 *                 description: Confidence score of the text extraction.
 *                 example: 0.95
 *         others:
 *           type: string
 *           nullable: true
 *           description: Additional information or description of the object.
 *           example: "Traditional outrigger design, likely used for small-scale fishing."
 *         aws_region:
 *           type: string
 *           nullable: true
 *           description: AWS region where the artifact is stored.
 *           example: "ap-southeast-1"
 *         video_path:
 *           type: string
 *           nullable: true
 *           description: Path to the artifact video in the S3 bucket.
 *           example: "artifacts/2024-09-21T07:22:34.941Z/video/prototype-33_cam-1_2024-09-21T08:41:43.736Z.mp4"
 *         imo_number:
 *           type: string
 *           nullable: true
 *           description: IMO number of the vessel.
 *           example: "9767890"
 *         weapons:
 *           type: string
 *           nullable: true
 *           description: Weapon of the artifact.
 *           example: "Missile"
 *         onboard_vessel_id:
 *           type: string
 *           nullable: true
 *           description: ID of the onboard vessel.
 *           example: "683df46b073245cf0fd62bb9"
 */

/**
 * @swagger
 * /artifacts/filters:
 *   get:
 *     summary: Fetch available artifact filters
 *     description: Retrieves distinct values for artifact filter fields, such as categories, country flags, onboard vessel names, and super categories.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of available filter options.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 countryFlags:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: Name of the country flag.
 *                         example: "Alaska"
 *                       code:
 *                         type: string
 *                         description: Code of the country flag.
 *                         example: "AL"
 *                   description: List of distinct country flags.
 *                   example: [{"name": "Alaska", "code": "AL"}, {"name": "Argentina", "code": "AR"}]
 *                 superCategories:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct super categories.
 *                   example: ["Cargo", "Fishing"]
 *                 sizes:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct sizes.
 *                   example: ["Small", "Medium", "Large"]
 *                 colors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of distinct colors.
 *                   example: ["Red", "Green", "Blue"]
 *                 weapons:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: Name of the weapon.
 *                         example: "Missile"
 *                       count:
 *                         type: integer
 *                         description: Frequency count of the weapon.
 *                         example: 15
 *                   description: List of distinct weapons with frequency > 10.
 *                   example: [{"name": "Missile", "count": 15}, {"name": "Gun", "count": 12}]
 *       401:
 *         description: Unauthorized. User must be authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Authentication error message.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts:
 *   post:
 *     summary: Fetch paginated artifacts
 *     description: Retrieves a paginated list of artifacts from the database based on applied filters. Supports filtering by time range, category, and exclusion of specific IDs.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               page:
 *                 type: integer
 *                 description: The page number for pagination (must be a positive integer).
 *                 example: 1
 *               pageSize:
 *                 type: integer
 *                 description: The number of results per page (must be a positive integer).
 *                 example: 20
 *               filters:
 *                 type: object
 *                 description: Additional filters to apply to the query.
 *                 properties:
 *                   start_time:
 *                     type: string
 *                     format: date-time
 *                     description: Start timestamp for filtering artifacts.
 *                     example: "2024-09-21T08:41:43.736Z"
 *                   end_time:
 *                     type: string
 *                     format: date-time
 *                     description: End timestamp for filtering artifacts.
 *                     example: "2024-09-22T08:41:43.736Z"
 *                   categories:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of super categories to filter artifacts.
 *                     example: ["Banca", "Trawler"]
 *                   id:
 *                     type: string
 *                     description: Filter by a specific artifact ID.
 *                     example: "66ef4f6ea6db303e08c0767c"
 *                   colors:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of colors to filter artifacts.
 *                     example: ["Red", "Blue"]
 *                   sizes:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of sizes to filter artifacts.
 *                     example: ["Small", "Large"]
 *                   type:
 *                     type: string
 *                     description: Type of artifact to filter ("video" or "image").
 *                     example: "image"
 *                   vessel_ids:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of vessel IDs to filter artifacts.
 *                     example: ["683df46b073245cf0fd62bb9"]
 *                   country_flags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of country flags to filter artifacts.
 *                     example: ["Alaska"]
 *                   weapons:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of weapons to filter artifacts.
 *                     example: ["Missile", "Gun"]
 *               favourites:
 *                 type: integer
 *                 description: Whether to include favourite artifacts data (0 or 1).
 *                 example: 1
 *               group:
 *                 type: integer
 *                 description: Whether to apply artifact grouping based on duplication_index (0 or 1).
 *                 example: 1
 *
 *     responses:
 *       200:
 *         description: A list of paginated artifacts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   description: Number of results per page.
 *                   example: 20
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of artifacts matching the query.
 *                   example: 100
 *                 groupedArtifacts:
 *                   type: array
 *                   description: Grouped artifact IDs (only included when group=1).
 *                   items:
 *                     type: array
 *                     items:
 *                       type: string
 *                   example: [["artifact1", "artifact2"], ["artifact3", "artifact4"]]
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts:
 *   post:
 *     summary: Fetch paginated artifacts
 *     description: Retrieves a paginated list of artifacts from the database based on applied filters. Supports filtering by time range, category, and exclusion of specific IDs.
 *     tags: [Artifacts]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *               page:
 *                 type: integer
 *                 description: The page number for pagination (must be a positive integer).
 *                 example: 1
 *               pageSize:
 *                 type: integer
 *                 description: The number of results per page (must be a positive integer).
 *                 example: 20
 *               filters:
 *                 type: object
 *                 description: Additional filters to apply to the query.
 *                 properties:
 *                   start_time:
 *                     type: string
 *                     format: date-time
 *                     description: Start timestamp for filtering artifacts.
 *                     example: "2024-09-21T08:41:43.736Z"
 *                   end_time:
 *                     type: string
 *                     format: date-time
 *                     description: End timestamp for filtering artifacts.
 *                     example: "2024-09-22T08:41:43.736Z"
 *                   categories:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of super categories to filter artifacts.
 *                     example: ["Banca", "Trawler"]
 *                   id:
 *                     type: string
 *                     description: Filter by a specific artifact ID.
 *                     example: "66ef4f6ea6db303e08c0767c"
 *                   colors:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of colors to filter artifacts.
 *                     example: ["Red", "Blue"]
 *                   sizes:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of sizes to filter artifacts.
 *                     example: ["Small", "Large"]
 *                   type:
 *                     type: string
 *                     description: Type of artifact to filter ("video" or "image").
 *                     example: "image"
 *                   vessel_ids:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of vessel IDs to filter artifacts.
 *                     example: ["683df46b073245cf0fd62bb9"]
 *                   country_flags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of country flags to filter artifacts.
 *                     example: ["Alaska"]
 *                   weapons:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of weapons to filter artifacts.
 *                     example: ["Missile", "Gun"]
 *               favourites:
 *                 type: integer
 *                 description: Whether to include favourite artifacts data (0 or 1).
 *                 example: 1
 *               group:
 *                 type: integer
 *                 description: Whether to apply artifact grouping based on duplication_index (0 or 1).
 *                 example: 1
 *
 *     responses:
 *       200:
 *         description: A list of paginated artifacts.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 artifacts:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Artifact'
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                   example: 1
 *                 pageSize:
 *                   type: integer
 *                   description: Number of results per page.
 *                   example: 20
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of artifacts matching the query.
 *                   example: 100
 *                 groupedArtifacts:
 *                   type: array
 *                   description: Grouped artifact IDs (only included when group=1).
 *                   items:
 *                     type: array
 *                     items:
 *                       type: string
 *                   example: [["artifact1", "artifact2"], ["artifact3", "artifact4"]]
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */

/**
 * @swagger
 * /artifacts/{vesselName}:
 *   post:
 *     summary: Fetch artifacts by vessel. (This route is deprecated, use v2 instead)
 *     description: Retrieves a list of artifacts for a specific vessel within a given time range. Supports filtering by time range, exclusion of specific IDs, and the option to include artifacts with null super categories.
 *     tags: [Artifacts]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         schema:
 *           type: string
 *         description: The name or ID of the vessel to fetch artifacts for.
 *         example: "prototype-33"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The start unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726876800000
 *               endTimestamp:
 *                 required: false
 *                 type: integer
 *                 description: The end unix timestamp in milliseconds for filtering artifacts.
 *                 example: 1726963200000
 *               excludeIds:
 *                 required: false
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: A list of artifact IDs to exclude from the results.
 *                 example: ["66ef4f6ea6db303e08c0767c", "66ef4f95a6db303e08c0767d"]
 *     responses:
 *       200:
 *         description: A list of artifacts for the specified vessel.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Artifact'
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       403:
 *         description: Cannot access this unit
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Cannot access this unit.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
