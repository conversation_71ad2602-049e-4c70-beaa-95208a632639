import { memo } from "react";
import { Grid } from "@mui/material";
import Card from "./Card";
import GroupedCard from "./GroupedCard";
import UnifiedGroupCard from "./UnifiedGroupCard";

const VirtualizedCardListRow = ({ index, style, data }) => {
    const { items, columnCount, CustomCard, setShowDetailModal, setSelectedCard, onFlaggedByClick, buttonsToShow, signedUrls } = data;
    const startIndex = index * columnCount;

    return (
        <Grid container style={style} spacing={2} paddingBottom={2}>
            {Array.from({ length: columnCount }).map((_, i) => {
                const eventIndex = startIndex + i;
                if (eventIndex >= items.length) return null;

                const artifact = items[eventIndex];
                const CardComponent = CustomCard || (artifact.duplications && artifact.duplications.length > 0 ? UnifiedGroupCard : artifact.isGroup ? GroupedCard : Card);

                // Compute grid size to distribute columns evenly across the row
                const colSize = 12 / columnCount;

                return (
                    <Grid
                        key={artifact._id || artifact.artifact?._id}
                        size={{
                            xs: 12,
                            sm: colSize,
                            md: colSize,
                            lg: colSize,
                            xl: colSize,
                        }}
                    >
                        <CardComponent
                            card={artifact}
                            setShowDetailModal={setShowDetailModal}
                            setSelectedCard={setSelectedCard}
                            onFlaggedByClick={onFlaggedByClick}
                            buttonsToShow={buttonsToShow}
                            signedUrls={signedUrls}
                        />
                    </Grid>
                );
            })}
        </Grid>
    );
};

VirtualizedCardListRow.displayName = "VirtualizedCardListRow";
export default memo(VirtualizedCardListRow);
